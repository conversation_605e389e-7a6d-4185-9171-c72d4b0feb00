/*
 * OpenSSL Version Compatibility:
 * - SSL_V3 defined: Uses OpenSSL 3.0+ EVP API (recommended)
 * - SSL_V3not defined: Uses legacy OpenSSL API (< 3.0)
 *
 * To enable OpenSSL 3.0+ support:
 * - CMake: -DENABLE_SSL_V3=ON (default)
 * - Makefile: Add -DSSL_V3 to CFLAGS
 */
#include "Encrypt.h"
#include <openssl/evp.h>
#include <cstring>

void Encrypt::set_key()
{
	memset(iv, 0x00, sizeof(iv));
	strcpy((char*)ckey, "GodWithSejongLee");
}

void Encrypt::init_ctr(struct ctr_state *state, const unsigned char *iv)
{
	state->num = 0;
    memset(state->ecount, 0, AES_BLOCK_SIZE);
    memset(state->ivec+16 , 0, 16);
    memcpy(state->ivec, iv, 16);
}

// encrypt == decrypt(내용은 같다)
#ifdef SSL_V3
// OpenSSL 3.0+ EVP version
void Encrypt::encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read) {
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    int len;
    int ciphertext_len;

    // Initialize context
    EVP_EncryptInit_ex(ctx, EVP_aes_128_ctr(), NULL, ckey, iv);

    // Perform encryption
    EVP_EncryptUpdate(ctx, outdata, &len, indata, bytes_read);
    ciphertext_len = len;

    // Finalize encryption
    EVP_EncryptFinal_ex(ctx, outdata + len, &len);
    ciphertext_len += len;

    // Free context
    EVP_CIPHER_CTX_free(ctx);
}
#else
// Legacy OpenSSL version (< 3.0)
void Encrypt::encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{
	int i=0;
	int mod_len=0;

	AES_set_encrypt_key(ckey, KEY_SIZE, &ase_key);

	if( bytes_read < BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
		return;
	}
	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 ){
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
	}
}
#endif

#ifdef SSL_V3
// OpenSSL 3.0+ EVP version
void Encrypt::decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read) {
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    int len;
    int plaintext_len;

    // Initialize context
    EVP_DecryptInit_ex(ctx, EVP_aes_128_ctr(), NULL, ckey, iv);

    // Perform decryption
    EVP_DecryptUpdate(ctx, outdata, &len, indata, bytes_read);
    plaintext_len = len;

    // Finalize decryption
    EVP_DecryptFinal_ex(ctx, outdata + len, &len);
    plaintext_len += len;

    // Free context
    EVP_CIPHER_CTX_free(ctx);
}
#else
// Legacy OpenSSL version (< 3.0)
void Encrypt::decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{
	int i=0;
	int mod_len=0;

	AES_set_encrypt_key(ckey, KEY_SIZE, &ase_key);

	if( bytes_read < BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
		return;
	}

	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 )
	{
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
	}

}
#endif

